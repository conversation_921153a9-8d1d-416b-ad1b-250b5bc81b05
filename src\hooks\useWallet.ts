'use client'

import { useState, useEffect, useCallback } from 'react'

// Dynamic imports to avoid SSR issues
let BrowserWallet: any = null
let Transaction: any = null

const loadMeshSDK = async () => {
  if (typeof window !== 'undefined' && !BrowserWallet) {
    try {
      const meshModule = await import('@meshsdk/core')
      BrowserWallet = meshModule.BrowserWallet
      Transaction = meshModule.Transaction
    } catch (error) {
      console.error('Failed to load Mesh SDK:', error)
    }
  }
}

interface WalletState {
  connected: boolean
  address: string | null
  balance: number | null
  trekBalance: number
  wallet: BrowserWallet | null
  connecting: boolean
  error: string | null
}

interface UseWalletReturn extends WalletState {
  connect: (walletName?: string) => Promise<void>
  disconnect: () => void
  getAvailableWallets: () => string[]
  sendTransaction: (tx: Transaction) => Promise<string>
  refreshBalance: () => Promise<void>
}

export function useWallet(): UseWalletReturn {
  const [state, setState] = useState<WalletState>({
    connected: false,
    address: null,
    balance: null,
    trekBalance: 0,
    wallet: null,
    connecting: false,
    error: null
  })

  // Get available wallets
  const getAvailableWallets = useCallback((): string[] => {
    if (typeof window === 'undefined') return []
    
    const wallets: string[] = []
    if (window.cardano?.lace) wallets.push('lace')
    if (window.cardano?.eternl) wallets.push('eternl')
    if (window.cardano?.nami) wallets.push('nami')
    if (window.cardano?.flint) wallets.push('flint')
    
    return wallets
  }, [])

  // Connect to wallet
  const connect = useCallback(async (walletName?: string) => {
    if (typeof window === 'undefined') return

    setState(prev => ({ ...prev, connecting: true, error: null }))

    try {
      // Load Mesh SDK first
      await loadMeshSDK()

      if (!BrowserWallet) {
        throw new Error('Failed to load Mesh SDK')
      }

      // If no wallet specified, try to connect to the first available
      const availableWallets = getAvailableWallets()
      const targetWallet = walletName || availableWallets[0]

      if (!targetWallet) {
        throw new Error('No Cardano wallet found. Please install Lace, Eternl, Nami, or Flint.')
      }

      const wallet = await BrowserWallet.enable(targetWallet)
      const addresses = await wallet.getUsedAddresses()
      const address = addresses[0]

      if (!address) {
        throw new Error('No addresses found in wallet')
      }

      setState(prev => ({
        ...prev,
        connected: true,
        address,
        wallet,
        connecting: false,
        error: null
      }))

      // Refresh balance after connection
      await refreshBalanceInternal(wallet, address)

    } catch (error) {
      console.error('Wallet connection error:', error)
      setState(prev => ({
        ...prev,
        connecting: false,
        error: error instanceof Error ? error.message : 'Failed to connect wallet'
      }))
    }
  }, [getAvailableWallets])

  // Disconnect wallet
  const disconnect = useCallback(() => {
    setState({
      connected: false,
      address: null,
      balance: null,
      trekBalance: 0,
      wallet: null,
      connecting: false,
      error: null
    })
  }, [])

  // Refresh balance
  const refreshBalanceInternal = useCallback(async (wallet: BrowserWallet, address: string) => {
    try {
      // Get ADA balance
      const balance = await wallet.getBalance()
      const adaBalance = parseInt(balance[0]?.quantity || '0') / 1000000 // Convert lovelace to ADA

      // Get TREK token balance (placeholder - would need actual token policy ID)
      // For now, using a mock value based on address
      const trekBalance = Math.floor(Math.random() * 1000) // Mock TREK balance

      setState(prev => ({
        ...prev,
        balance: adaBalance,
        trekBalance
      }))
    } catch (error) {
      console.error('Error refreshing balance:', error)
    }
  }, [])

  const refreshBalance = useCallback(async () => {
    if (state.wallet && state.address) {
      await refreshBalanceInternal(state.wallet, state.address)
    }
  }, [state.wallet, state.address, refreshBalanceInternal])

  // Send transaction
  const sendTransaction = useCallback(async (tx: Transaction): Promise<string> => {
    if (!state.wallet) {
      throw new Error('Wallet not connected')
    }

    try {
      const signedTx = await state.wallet.signTx(tx.toCbor())
      const txHash = await state.wallet.submitTx(signedTx)
      return txHash
    } catch (error) {
      console.error('Transaction error:', error)
      throw error
    }
  }, [state.wallet])

  // Auto-connect on mount if wallet was previously connected
  useEffect(() => {
    const autoConnect = async () => {
      const lastConnectedWallet = localStorage.getItem('vintrek-wallet')
      if (lastConnectedWallet && getAvailableWallets().includes(lastConnectedWallet)) {
        await connect(lastConnectedWallet)
      }
    }

    autoConnect()
  }, [connect, getAvailableWallets])

  // Save connected wallet to localStorage
  useEffect(() => {
    if (state.connected && state.wallet) {
      // Try to determine which wallet is connected (this is a simplified approach)
      const availableWallets = getAvailableWallets()
      if (availableWallets.length > 0) {
        localStorage.setItem('vintrek-wallet', availableWallets[0])
      }
    } else {
      localStorage.removeItem('vintrek-wallet')
    }
  }, [state.connected, state.wallet, getAvailableWallets])

  return {
    ...state,
    connect,
    disconnect,
    getAvailableWallets,
    sendTransaction,
    refreshBalance
  }
}
