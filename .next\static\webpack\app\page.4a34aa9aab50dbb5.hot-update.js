"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useWallet.ts":
/*!********************************!*\
  !*** ./src/hooks/useWallet.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWallet auto */ \n// Dynamic imports to avoid SSR issues\nlet BrowserWallet = null;\nlet Transaction = null;\nconst loadMeshSDK = async ()=>{\n    if ( true && !BrowserWallet) {\n        try {\n            const meshModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\"));\n            BrowserWallet = meshModule.BrowserWallet;\n            Transaction = meshModule.Transaction;\n        } catch (error) {\n            console.error('Failed to load Mesh SDK:', error);\n        }\n    }\n};\nfunction useWallet() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        connected: false,\n        address: null,\n        balance: null,\n        trekBalance: 0,\n        wallet: null,\n        connecting: false,\n        error: null\n    });\n    // Get available wallets\n    const getAvailableWallets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[getAvailableWallets]\": ()=>{\n            var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n            if (false) {}\n            const wallets = [];\n            if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n            if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n            if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n            if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n            return wallets;\n        }\n    }[\"useWallet.useCallback[getAvailableWallets]\"], []);\n    // Connect to wallet\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[connect]\": async (walletName)=>{\n            if (false) {}\n            setState({\n                \"useWallet.useCallback[connect]\": (prev)=>({\n                        ...prev,\n                        connecting: true,\n                        error: null\n                    })\n            }[\"useWallet.useCallback[connect]\"]);\n            try {\n                // Load Mesh SDK first\n                await loadMeshSDK();\n                if (!BrowserWallet) {\n                    throw new Error('Failed to load Mesh SDK');\n                }\n                // If no wallet specified, try to connect to the first available\n                const availableWallets = getAvailableWallets();\n                const targetWallet = walletName || availableWallets[0];\n                if (!targetWallet) {\n                    throw new Error('No Cardano wallet found. Please install Lace, Eternl, Nami, or Flint.');\n                }\n                const wallet = await BrowserWallet.enable(targetWallet);\n                const addresses = await wallet.getUsedAddresses();\n                const address = addresses[0];\n                if (!address) {\n                    throw new Error('No addresses found in wallet');\n                }\n                setState({\n                    \"useWallet.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            connected: true,\n                            address,\n                            wallet,\n                            connecting: false,\n                            error: null\n                        })\n                }[\"useWallet.useCallback[connect]\"]);\n                // Refresh balance after connection\n                await refreshBalanceInternal(wallet, address);\n            } catch (error) {\n                console.error('Wallet connection error:', error);\n                setState({\n                    \"useWallet.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            connecting: false,\n                            error: error instanceof Error ? error.message : 'Failed to connect wallet'\n                        })\n                }[\"useWallet.useCallback[connect]\"]);\n            }\n        }\n    }[\"useWallet.useCallback[connect]\"], [\n        getAvailableWallets\n    ]);\n    // Disconnect wallet\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[disconnect]\": ()=>{\n            setState({\n                connected: false,\n                address: null,\n                balance: null,\n                trekBalance: 0,\n                wallet: null,\n                connecting: false,\n                error: null\n            });\n        }\n    }[\"useWallet.useCallback[disconnect]\"], []);\n    // Refresh balance\n    const refreshBalanceInternal = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[refreshBalanceInternal]\": async (wallet, address)=>{\n            try {\n                var _balance_;\n                // Get ADA balance\n                const balance = await wallet.getBalance();\n                const adaBalance = parseInt(((_balance_ = balance[0]) === null || _balance_ === void 0 ? void 0 : _balance_.quantity) || '0') / 1000000 // Convert lovelace to ADA\n                ;\n                // Get TREK token balance (placeholder - would need actual token policy ID)\n                // For now, using a mock value based on address\n                const trekBalance = Math.floor(Math.random() * 1000) // Mock TREK balance\n                ;\n                setState({\n                    \"useWallet.useCallback[refreshBalanceInternal]\": (prev)=>({\n                            ...prev,\n                            balance: adaBalance,\n                            trekBalance\n                        })\n                }[\"useWallet.useCallback[refreshBalanceInternal]\"]);\n            } catch (error) {\n                console.error('Error refreshing balance:', error);\n            }\n        }\n    }[\"useWallet.useCallback[refreshBalanceInternal]\"], []);\n    const refreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[refreshBalance]\": async ()=>{\n            if (state.wallet && state.address) {\n                await refreshBalanceInternal(state.wallet, state.address);\n            }\n        }\n    }[\"useWallet.useCallback[refreshBalance]\"], [\n        state.wallet,\n        state.address,\n        refreshBalanceInternal\n    ]);\n    // Send transaction\n    const sendTransaction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWallet.useCallback[sendTransaction]\": async (tx)=>{\n            if (!state.wallet) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const signedTx = await state.wallet.signTx(tx.toCbor());\n                const txHash = await state.wallet.submitTx(signedTx);\n                return txHash;\n            } catch (error) {\n                console.error('Transaction error:', error);\n                throw error;\n            }\n        }\n    }[\"useWallet.useCallback[sendTransaction]\"], [\n        state.wallet\n    ]);\n    // Auto-connect on mount if wallet was previously connected\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWallet.useEffect\": ()=>{\n            const autoConnect = {\n                \"useWallet.useEffect.autoConnect\": async ()=>{\n                    const lastConnectedWallet = localStorage.getItem('vintrek-wallet');\n                    if (lastConnectedWallet && getAvailableWallets().includes(lastConnectedWallet)) {\n                        await connect(lastConnectedWallet);\n                    }\n                }\n            }[\"useWallet.useEffect.autoConnect\"];\n            autoConnect();\n        }\n    }[\"useWallet.useEffect\"], [\n        connect,\n        getAvailableWallets\n    ]);\n    // Save connected wallet to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWallet.useEffect\": ()=>{\n            if (state.connected && state.wallet) {\n                // Try to determine which wallet is connected (this is a simplified approach)\n                const availableWallets = getAvailableWallets();\n                if (availableWallets.length > 0) {\n                    localStorage.setItem('vintrek-wallet', availableWallets[0]);\n                }\n            } else {\n                localStorage.removeItem('vintrek-wallet');\n            }\n        }\n    }[\"useWallet.useEffect\"], [\n        state.connected,\n        state.wallet,\n        getAvailableWallets\n    ]);\n    return {\n        ...state,\n        connect,\n        disconnect,\n        getAvailableWallets,\n        sendTransaction,\n        refreshBalance\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWallet.ts\n"));

/***/ })

});